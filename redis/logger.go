/**
 * @note
 * logger
 *
 * <AUTHOR>
 * @date 	2025-06-10
 */
package redis

import (
	"context"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisLoggerHook struct {
	PrintLog bool
}

func NewRedisLoggerHook(printLog bool) *RedisLoggerHook {
	return &RedisLoggerHook{PrintLog: printLog}
}

func (h *RedisLoggerHook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	if !h.PrintLog {
		return ctx, nil
	}
	ctx = context.WithValue(ctx, "redisStart", hlog.GetLogField(hlog.LogTagRedisOk))
	return ctx, nil
}

func (h *RedisLoggerHook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	if !h.PrintLog {
		return nil
	}
	logField := ctx.Value("redisStart").(logrus.Fields)
}

func (h *RedisLoggerHook) BeforeProcessPipeline(ctx context.Context, cmds []redis.Cmder) (context.Context, error) {
	if !h.PrintLog {
		return ctx, nil
	}
	ctx = context.WithValue(ctx, "redisStart", hlog.GetLogField(hlog.LogTagRedisOk))
	return ctx, nil
}

func (h *RedisLoggerHook) AfterProcessPipeline(ctx context.Context, cmds []redis.Cmder) error {
	if !h.PrintLog {
		return nil
	}
	logField := ctx.Value("redisStart").(logrus.Fields)
}
