/**
 * @note
 * Redis Hook 日志记录器
 *
 * <AUTHOR>
 * @date 	2025-06-10
 */
package redis

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/logger"
)

type RedisLoggerHook struct {
	PrintLog bool
}

func NewRedisLoggerHook(printLog bool) *RedisLoggerHook {
	return &RedisLoggerHook{PrintLog: printLog}
}

func (h *RedisLoggerHook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	if !h.PrintLog {
		return ctx, nil
	}
	// 记录开始时间和日志字段
	ctx = context.WithValue(ctx, "redisLogField", hlog.GetLogField(common.LogTagRedisOK))
	return ctx, nil
}

func (h *RedisLoggerHook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	if !h.PrintLog {
		return nil
	}

	logField, ok := ctx.Value("redisLogField").(logrus.Fields)
	if !ok {
		return nil
	}


	// 构建日志字段
	f := logrus.Fields{
		"api":       strings.ToUpper(cmd.Name()),
		"proc_time": procTime,
	}

	// 根据命令类型添加特定字段
	h.addCommandSpecificFields(f, cmd)

	// 记录错误信息
	var err error
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		err = common.NewError(common.ErrCodeSys, cmd.Err())
	} else {
		err = common.NewError(common.ErrCodeOK)
	}

	// 打印日志
	logger.PrintLogWithError(err, common.GetLogger(ctx), logField, f)

	return nil
}

func (h *RedisLoggerHook) BeforeProcessPipeline(ctx context.Context, cmds []redis.Cmder) (context.Context, error) {
	if !h.PrintLog {
		return ctx, nil
	}
	// 记录开始时间和日志字段
	ctx = context.WithValue(ctx, "redisStartTime", time.Now())
	ctx = context.WithValue(ctx, "redisLogField", hlog.GetLogField(common.LogTagRedisOK))
	return ctx, nil
}

func (h *RedisLoggerHook) AfterProcessPipeline(ctx context.Context, cmds []redis.Cmder) error {
	if !h.PrintLog {
		return nil
	}

	// 获取开始时间和日志字段
	startTime, ok := ctx.Value("redisStartTime").(time.Time)
	if !ok {
		return nil
	}
	logField, ok := ctx.Value("redisLogField").(logrus.Fields)
	if !ok {
		return nil
	}

	// 计算执行时间
	procTime := float64(time.Since(startTime).Nanoseconds()) / 1e6

	// 构建管道命令列表
	cmdNames := make([]string, len(cmds))
	for i, cmd := range cmds {
		cmdNames[i] = strings.ToUpper(cmd.Name())
	}

	// 构建日志字段
	f := logrus.Fields{
		"api":       "PIPELINE",
		"commands":  cmdNames,
		"count":     len(cmds),
		"proc_time": procTime,
	}

	// 检查是否有错误
	var err error
	hasError := false
	for _, cmd := range cmds {
		if cmd.Err() != nil && cmd.Err() != redis.Nil {
			hasError = true
			break
		}
	}

	if hasError {
		err = common.NewError(common.ErrCodeSys, "pipeline has errors")
	} else {
		err = common.NewError(common.ErrCodeOK)
	}

	// 打印日志
	logger.PrintLogWithError(err, common.GetLogger(ctx), logField, f)

	return nil
}

// addCommandSpecificFields 根据不同的 Redis 命令添加特定的日志字段
func (h *RedisLoggerHook) addCommandSpecificFields(f logrus.Fields, cmd redis.Cmder) {
	cmdName := strings.ToLower(cmd.Name())
	args := cmd.Args()

	switch cmdName {
	case "set":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["value"] = args[2]
			if len(args) >= 5 {
				f["expire"] = fmt.Sprintf("%v %v", args[3], args[4])
			}
		}
	case "setnx":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["value"] = args[2]
			if setNXCmd, ok := cmd.(*redis.BoolCmd); ok {
				f["success"] = setNXCmd.Val()
			}
		}
	case "get":
		if len(args) >= 2 {
			f["key"] = args[1]
			if stringCmd, ok := cmd.(*redis.StringCmd); ok {
				f["output"] = stringCmd.Val()
			}
		}
	case "mget":
		if len(args) >= 2 {
			f["keys"] = args[1:]
			if sliceCmd, ok := cmd.(*redis.SliceCmd); ok {
				f["output"] = fmt.Sprintf("%v", sliceCmd.Val())
			}
		}
	case "del":
		if len(args) >= 2 {
			f["keys"] = args[1:]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["effectRows"] = intCmd.Val()
			}
		}
	case "expire", "pexpire":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["expire"] = args[2]
		}
	case "ttl", "pttl":
		if len(args) >= 2 {
			f["key"] = args[1]
			if durationCmd, ok := cmd.(*redis.DurationCmd); ok {
				f["output"] = durationCmd.Val()
			}
		}
	case "incr", "decr":
		if len(args) >= 2 {
			f["key"] = args[1]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["result"] = intCmd.Val()
			}
		}
	case "incrby", "decrby":
		if len(args) >= 3 {
			f["key"] = args[1]
			if cmdName == "incrby" {
				f["increment"] = args[2]
			} else {
				f["decrement"] = args[2]
			}
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["result"] = intCmd.Val()
			}
		}
	case "lpop", "rpop":
		if len(args) >= 2 {
			f["key"] = args[1]
			if stringCmd, ok := cmd.(*redis.StringCmd); ok {
				f["output"] = stringCmd.Val()
			}
		}
	case "lpush", "rpush":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["value"] = args[2:]
		}
	case "llen":
		if len(args) >= 2 {
			f["key"] = args[1]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["output"] = intCmd.Val()
			}
		}
	case "lrange":
		if len(args) >= 4 {
			f["key"] = args[1]
			f["start"] = args[2]
			f["stop"] = args[3]
			if stringSliceCmd, ok := cmd.(*redis.StringSliceCmd); ok {
				f["output"] = stringSliceCmd.Val()
			}
		}
	case "hset", "hmset":
		if len(args) >= 3 {
			f["key"] = args[1]
			// 对于 hash 操作，args[2:] 包含字段和值
			f["valuesMap"] = args[2:]
		}
	case "hsetnx":
		if len(args) >= 4 {
			f["key"] = args[1]
			f["field"] = args[2]
			f["value"] = args[3]
			if boolCmd, ok := cmd.(*redis.BoolCmd); ok {
				f["success"] = boolCmd.Val()
			}
		}
	case "hget":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["field"] = args[2]
			if stringCmd, ok := cmd.(*redis.StringCmd); ok {
				f["output"] = stringCmd.Val()
			}
		}
	case "hmget":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["fields"] = args[2:]
			if sliceCmd, ok := cmd.(*redis.SliceCmd); ok {
				f["output"] = sliceCmd.Val()
			}
		}
	case "hdel":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["fields"] = args[2:]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["effectRows"] = intCmd.Val()
			}
		}
	case "hkeys":
		if len(args) >= 2 {
			f["key"] = args[1]
			if stringSliceCmd, ok := cmd.(*redis.StringSliceCmd); ok {
				f["output"] = stringSliceCmd.Val()
			}
		}
	case "hvals":
		if len(args) >= 2 {
			f["key"] = args[1]
			if stringSliceCmd, ok := cmd.(*redis.StringSliceCmd); ok {
				f["output"] = stringSliceCmd.Val()
			}
		}
	case "hlen":
		if len(args) >= 2 {
			f["key"] = args[1]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["output"] = intCmd.Val()
			}
		}
	case "hgetall":
		if len(args) >= 2 {
			f["key"] = args[1]
			if stringStringMapCmd, ok := cmd.(*redis.StringStringMapCmd); ok {
				f["output"] = stringStringMapCmd.Val()
			}
		}
	case "hincrby":
		if len(args) >= 4 {
			f["key"] = args[1]
			f["field"] = args[2]
			f["increment"] = args[3]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["result"] = intCmd.Val()
			}
		}
	case "hscan":
		if len(args) >= 4 {
			f["key"] = args[1]
			f["cursor"] = args[2]
			if len(args) >= 6 {
				f["match"] = args[4]
				f["count"] = args[5]
			}
			if scanCmd, ok := cmd.(*redis.ScanCmd); ok {
				keys, _, _ := scanCmd.Result()
				f["output"] = keys
			}
		}
	case "zincrby":
		if len(args) >= 4 {
			f["key"] = args[1]
			f["increment"] = args[2]
			f["member"] = args[3]
			if floatCmd, ok := cmd.(*redis.FloatCmd); ok {
				f["output"] = int64(floatCmd.Val())
			}
		}
	case "zrange", "zrevrange":
		if len(args) >= 4 {
			f["key"] = args[1]
			f["start"] = args[2]
			f["stop"] = args[3]
			// 检查是否带有 WITHSCORES
			if len(args) >= 5 && strings.ToUpper(fmt.Sprintf("%v", args[4])) == "WITHSCORES" {
				if zSliceCmd, ok := cmd.(*redis.ZSliceCmd); ok {
					f["output"] = zSliceCmd.Val()
				}
			}
		}
	case "zcard":
		if len(args) >= 2 {
			f["key"] = args[1]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["output"] = intCmd.Val()
			}
		}
	case "zremrangebyrank":
		if len(args) >= 4 {
			f["key"] = args[1]
			f["start"] = args[2]
			f["stop"] = args[3]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["output"] = intCmd.Val()
			}
		}
	case "zrem":
		if len(args) >= 3 {
			f["key"] = args[1]
			f["members"] = args[2:]
			if intCmd, ok := cmd.(*redis.IntCmd); ok {
				f["output"] = intCmd.Val()
			}
		}
	default:
		// 对于其他命令，记录基本信息
		if len(args) >= 2 {
			f["key"] = args[1]
		}
		if len(args) >= 3 {
			f["args"] = args[2:]
		}
	}
}
