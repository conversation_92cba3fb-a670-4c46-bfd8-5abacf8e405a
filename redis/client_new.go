/**
 * @note
 * client.go - 使用 Redis v9 Hook 进行日志记录的简化版本
 *
 * <AUTHOR>
 * @date 	2020-11-27
 */
package redis

import (
	"context"
	"crypto/tls"
	"errors"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

type Client struct {
	ctx           context.Context
	client        *redis.Client
	clusterClient *redis.ClusterClient
	isCluster     bool
	PrintLog      bool
}

func (c *Client) WithContext(ctx context.Context) *Client {
	c.ctx = ctx
	return c
}

func (c *Client) Context() context.Context {
	return c.ctx
}

func newClient(ctx context.Context, conf RedisConfig) (*Client, error) {
	if len(conf.Servers) == 0 {
		return nil, errors.New("invalid servers")
	}
	c := &Client{ctx: ctx, isCluster: conf.Cluster, PrintLog: true} // 默认开启日志

	// 创建日志 hook
	loggerHook := NewRedisLoggerHook(c.PrintLog)

	if conf.Cluster {
		c.clusterClient = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:          conf.Servers,
			Username:       conf.User,
			Password:       conf.Password,
			MaxRetries:     conf.MaxRetries,
			DialTimeout:    conf.dialTimeoutT,
			ReadTimeout:    conf.readTimeoutT,
			WriteTimeout:   conf.writeTimeoutT,
			PoolSize:       conf.PoolSize,
			MinIdleConns:   conf.MinIdle,
			PoolTimeout:    conf.poolTimeoutT,
			MaxRedirects:   conf.MaxRedirects,
			RouteByLatency: conf.RouteByLatency,
			RouteRandomly:  conf.RouteRandomly,
			TLSConfig: func(useTLS bool) *tls.Config {
				if useTLS {
					return &tls.Config{InsecureSkipVerify: true}
				}
				return nil
			}(conf.TLS),
		})
		// 添加日志 hook
		c.clusterClient.AddHook(loggerHook)
	} else {
		c.client = redis.NewClient(&redis.Options{
			Addr:         conf.Servers[0],
			Username:     conf.User,
			Password:     conf.Password,
			DB:           conf.Database,
			MaxRetries:   conf.MaxRetries,
			DialTimeout:  conf.dialTimeoutT,
			ReadTimeout:  conf.readTimeoutT,
			WriteTimeout: conf.writeTimeoutT,
			PoolSize:     conf.PoolSize,
			MinIdleConns: conf.MinIdle,
			PoolTimeout:  conf.poolTimeoutT,
			TLSConfig: func(useTLS bool) *tls.Config {
				if useTLS {
					return &tls.Config{InsecureSkipVerify: true}
				}
				return nil
			}(conf.TLS),
		})
		// 添加日志 hook
		c.client.AddHook(loggerHook)
	}
	return c, nil
}

// Print 方法保留以保持兼容性，但不再使用，因为现在使用 hook 记录日志
func (c *Client) Print(err error, startField, endField logrus.Fields) {
	// 不再使用，由 hook 处理
}

// SET expiration = -1 时为keepTTL
func (c *Client) Set(key string, value interface{}, expiration time.Duration) error {
	if c.isCluster {
		return c.clusterClient.Set(c.Context(), key, value, expiration).Err()
	} else {
		return c.client.Set(c.Context(), key, value, expiration).Err()
	}
}

func (c *Client) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	if c.isCluster {
		return c.clusterClient.SetNX(c.Context(), key, value, expiration).Result()
	} else {
		return c.client.SetNX(c.Context(), key, value, expiration).Result()
	}
}

func (c *Client) Get(key string) (*redis.StringCmd, error) {
	var ret *redis.StringCmd
	if c.isCluster {
		ret = c.clusterClient.Get(c.Context(), key)
	} else {
		ret = c.client.Get(c.Context(), key)
	}
	return ret, ret.Err()
}

func (c *Client) MGet(keys ...string) ([]interface{}, error) {
	if c.isCluster {
		return c.clusterClient.MGet(c.Context(), keys...).Result()
	} else {
		return c.client.MGet(c.Context(), keys...).Result()
	}
}

func (c *Client) Expire(key string, expiration time.Duration) (bool, error) {
	if c.isCluster {
		return c.clusterClient.Expire(c.Context(), key, expiration).Result()
	} else {
		return c.client.Expire(c.Context(), key, expiration).Result()
	}
}

func (c *Client) PExpire(key string, expiration time.Duration) (bool, error) {
	if c.isCluster {
		return c.clusterClient.PExpire(c.Context(), key, expiration).Result()
	} else {
		return c.client.PExpire(c.Context(), key, expiration).Result()
	}
}

// INCRBY
func (c *Client) IncrBy(key string, value int64) (int64, error) {
	if c.isCluster {
		return c.clusterClient.IncrBy(c.Context(), key, value).Result()
	} else {
		return c.client.IncrBy(c.Context(), key, value).Result()
	}
}

// INCR
func (c *Client) Incr(key string) (int64, error) {
	if c.isCluster {
		return c.clusterClient.Incr(c.Context(), key).Result()
	} else {
		return c.client.Incr(c.Context(), key).Result()
	}
}

// DECR
func (c *Client) Decr(key string) (int64, error) {
	if c.isCluster {
		return c.clusterClient.Decr(c.Context(), key).Result()
	} else {
		return c.client.Decr(c.Context(), key).Result()
	}
}

// DECRBY
func (c *Client) DecrBy(key string, value int64) (int64, error) {
	if c.isCluster {
		return c.clusterClient.DecrBy(c.Context(), key, value).Result()
	} else {
		return c.client.DecrBy(c.Context(), key, value).Result()
	}
}

// TTL
func (c *Client) TTL(key string) (time.Duration, error) {
	if c.isCluster {
		return c.clusterClient.TTL(c.Context(), key).Result()
	} else {
		return c.client.TTL(c.Context(), key).Result()
	}
}

// PTTL
func (c *Client) PTTL(key string) (time.Duration, error) {
	if c.isCluster {
		return c.clusterClient.PTTL(c.Context(), key).Result()
	} else {
		return c.client.PTTL(c.Context(), key).Result()
	}
}

// DEL
func (c *Client) Del(keys ...string) (int64, error) {
	if c.isCluster {
		return c.clusterClient.Del(c.Context(), keys...).Result()
	} else {
		return c.client.Del(c.Context(), keys...).Result()
	}
}

// LPOP
func (c *Client) LPop(key string) (*redis.StringCmd, error) {
	var ret *redis.StringCmd
	if c.isCluster {
		ret = c.clusterClient.LPop(c.Context(), key)
	} else {
		ret = c.client.LPop(c.Context(), key)
	}
	return ret, ret.Err()
}

// LPUSH
func (c *Client) LPush(key string, values ...interface{}) (int64, error) {
	if c.isCluster {
		return c.clusterClient.LPush(c.Context(), key, values...).Result()
	} else {
		return c.client.LPush(c.Context(), key, values...).Result()
	}
}

// RPOP
func (c *Client) RPop(key string) (*redis.StringCmd, error) {
	var ret *redis.StringCmd
	if c.isCluster {
		ret = c.clusterClient.RPop(c.Context(), key)
	} else {
		ret = c.client.RPop(c.Context(), key)
	}
	return ret, ret.Err()
}

// RPUSH
func (c *Client) RPush(key string, values ...interface{}) (int64, error) {
	if c.isCluster {
		return c.clusterClient.RPush(c.Context(), key, values...).Result()
	} else {
		return c.client.RPush(c.Context(), key, values...).Result()
	}
}

// LLEN
func (c *Client) LLen(key string) (int64, error) {
	if c.isCluster {
		return c.clusterClient.LLen(c.Context(), key).Result()
	} else {
		return c.client.LLen(c.Context(), key).Result()
	}
}

// LRange
func (c *Client) LRange(key string, start, stop int64) ([]string, error) {
	if c.isCluster {
		return c.clusterClient.LRange(c.Context(), key, start, stop).Result()
	} else {
		return c.client.LRange(c.Context(), key, start, stop).Result()
	}
}

// HSET
func (c *Client) HSet(key string, valuesMap map[string]interface{}) error {
	if c.isCluster {
		return c.clusterClient.HSet(c.Context(), key, valuesMap).Err()
	} else {
		return c.client.HSet(c.Context(), key, valuesMap).Err()
	}
}

// HMSET
func (c *Client) HMSet(key string, valuesMap map[string]interface{}) error {
	if c.isCluster {
		return c.clusterClient.HMSet(c.Context(), key, valuesMap).Err()
	} else {
		return c.client.HMSet(c.Context(), key, valuesMap).Err()
	}
}

func (c *Client) HSetNX(key, field string, value interface{}) (bool, error) {
	if c.isCluster {
		return c.clusterClient.HSetNX(c.Context(), key, field, value).Result()
	} else {
		return c.client.HSetNX(c.Context(), key, field, value).Result()
	}
}

func (c *Client) HIncrBy(key, field string, value int64) (int64, error) {
	if c.isCluster {
		return c.clusterClient.HIncrBy(c.Context(), key, field, value).Result()
	} else {
		return c.client.HIncrBy(c.Context(), key, field, value).Result()
	}
}

func (c *Client) HGet(key, field string) (*redis.StringCmd, error) {
	var ret *redis.StringCmd
	if c.isCluster {
		ret = c.clusterClient.HGet(c.Context(), key, field)
	} else {
		ret = c.client.HGet(c.Context(), key, field)
	}
	return ret, ret.Err()
}

func (c *Client) HScan(key string, cursor uint64, match string, count int64) ([]string, uint64, error) {
	if c.isCluster {
		return c.clusterClient.HScan(c.Context(), key, cursor, match, count).Result()
	} else {
		return c.client.HScan(c.Context(), key, cursor, match, count).Result()
	}
}

func (c *Client) HMGet(key string, fields ...string) (*redis.SliceCmd, error) {
	var ret *redis.SliceCmd
	if c.isCluster {
		ret = c.clusterClient.HMGet(c.Context(), key, fields...)
	} else {
		ret = c.client.HMGet(c.Context(), key, fields...)
	}
	return ret, ret.Err()
}

// HDEL
func (c *Client) HDel(key string, fields ...string) (int64, error) {
	if c.isCluster {
		return c.clusterClient.HDel(c.Context(), key, fields...).Result()
	} else {
		return c.client.HDel(c.Context(), key, fields...).Result()
	}
}

func (c *Client) HKeys(key string) ([]string, error) {
	if c.isCluster {
		return c.clusterClient.HKeys(c.Context(), key).Result()
	} else {
		return c.client.HKeys(c.Context(), key).Result()
	}
}

func (c *Client) HVals(key string) ([]string, error) {
	if c.isCluster {
		return c.clusterClient.HVals(c.Context(), key).Result()
	} else {
		return c.client.HVals(c.Context(), key).Result()
	}
}

func (c *Client) HLen(key string) (int64, error) {
	if c.isCluster {
		return c.clusterClient.HLen(c.Context(), key).Result()
	} else {
		return c.client.HLen(c.Context(), key).Result()
	}
}

func (c *Client) HGetAll(key string) (map[string]string, error) {
	if c.isCluster {
		return c.clusterClient.HGetAll(c.Context(), key).Result()
	} else {
		return c.client.HGetAll(c.Context(), key).Result()
	}
}

func (c *Client) ZIncrBy(key, member string, value int64) (int64, error) {
	var result float64
	var err error
	if c.isCluster {
		result, err = c.clusterClient.ZIncrBy(c.Context(), key, float64(value), member).Result()
	} else {
		result, err = c.client.ZIncrBy(c.Context(), key, float64(value), member).Result()
	}
	return int64(result), err
}

func (c *Client) ZRangeWithScores(key string, start, stop int64) ([]redis.Z, error) {
	if c.isCluster {
		return c.clusterClient.ZRangeWithScores(c.Context(), key, start, stop).Result()
	} else {
		return c.client.ZRangeWithScores(c.Context(), key, start, stop).Result()
	}
}

func (c *Client) ZRevRangeWithScores(key string, start, stop int64) ([]redis.Z, error) {
	if c.isCluster {
		return c.clusterClient.ZRevRangeWithScores(c.Context(), key, start, stop).Result()
	} else {
		return c.client.ZRevRangeWithScores(c.Context(), key, start, stop).Result()
	}
}

func (c *Client) ZCard(key string) (int64, error) {
	if c.isCluster {
		return c.clusterClient.ZCard(c.Context(), key).Result()
	} else {
		return c.client.ZCard(c.Context(), key).Result()
	}
}

func (c *Client) ZRemRangeByRank(key string, start, stop int64) (int64, error) {
	if c.isCluster {
		return c.clusterClient.ZRemRangeByRank(c.Context(), key, start, stop).Result()
	} else {
		return c.client.ZRemRangeByRank(c.Context(), key, start, stop).Result()
	}
}

func (c *Client) ZRem(key string, members ...interface{}) (int64, error) {
	if c.isCluster {
		return c.clusterClient.ZRem(c.Context(), key, members...).Result()
	} else {
		return c.client.ZRem(c.Context(), key, members...).Result()
	}
}
