/**
 * @note
 * http_client
 *
 * <AUTHOR>
 * @date 	2025-06-10
 */
package middleware

import (
	"github.com/prometheus/client_golang/prometheus"
	"sync"
)

var (
	httpClientRequestDuration *prometheus.HistogramVec
	httpClientResponseStatus  *prometheus.CounterVec
	clientOnce                sync.Once
)

func registerHttpClientMetrics() {
	// 注册指标
	clientOnce.Do(func() {
		httpClientRequestDuration = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: GetPrometheusConfig().Namespace,
				Subsystem: GetPrometheusConfig().Subsystem,
				Name:      "http_client_duration_seconds",
				Help:      "Duration of HTTP client requests",
				Buckets:   prometheus.DefBuckets,
			},
			[]string{"host", "path", "method"},
		)

		httpClientResponseStatus = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: GetPrometheusConfig().Namespace,
				Subsystem: GetPrometheusConfig().Subsystem,
				Name:      "http_client_status_total",
				Help:      "HTTP client response status codes",
			},
			[]string{"host", "path", "method", "status"},
		)
		prometheus.MustRegister(httpClientRequestDuration, httpClientResponseStatus)
	})
}

func PrometheusMiddleware()