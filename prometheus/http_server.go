/**
 * @note
 * prometheus
 *
 * <AUTHOR>
 * @date 	2025-05-30
 */
package prometheus

import (
	"gitlab.docsl.com/security/common"
	"strconv"
	"sync"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/prometheus/client_golang/prometheus"
)

var (
	httpServerRequestDuration *prometheus.HistogramVec
	httpServerResponseStatus  *prometheus.CounterVec
	serverOnce                sync.Once
)

func registerHttpServerMetrics() {
	// 注册指标
	serverOnce.Do(func() {
		httpServerRequestDuration = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: GetPrometheusConfig().Namespace,
				Subsystem: GetPrometheusConfig().Subsystem,
				Name:      "http_server_duration_seconds",
				Help:      "Duration of HTTP server requests",
				Buckets:   prometheus.DefBuckets,
			},
			[]string{"path", "method"},
		)

		httpServerResponseStatus = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: GetPrometheusConfig().Namespace,
				Subsystem: GetPrometheusConfig().Subsystem,
				Name:      "http_server_status_total",
				Help:      "HTTP server response status codes",
			},
			[]string{"path", "method", "status", "biz_code"},
		)
		prometheus.MustRegister(httpServerRequestDuration, httpServerResponseStatus)
	})
}

func HttpServerPrometheusMiddleware() iris.Handler {
	registerHttpServerMetrics()
	return func(ctx iris.Context) {
		if GetPrometheusConfig().Addr == common.StringEmpty {
			return
		}
		start := time.Now()

		ctx.Next()

		duration := time.Since(start).Seconds()
		path := ctx.Request().URL.Path
		method := ctx.Method()
		statusCode := strconv.Itoa(ctx.GetStatusCode())
		var bizCode string
		ret := common.GetRet(ctx)
		switch ret := ret.(type) {
		case *common.Error:
			bizCode = strconv.Itoa(ret.Code)
		case common.Error:
			bizCode = strconv.Itoa(ret.Code)
		}
		httpServerRequestDuration.WithLabelValues(path, method).Observe(duration)
		httpServerResponseStatus.WithLabelValues(path, method, statusCode, bizCode).Inc()
	}
}
