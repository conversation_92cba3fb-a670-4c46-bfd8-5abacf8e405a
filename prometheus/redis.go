/**
 * @note
 * redis
 *
 * <AUTHOR>
 * @date 	2025-06-10
 */
package prometheus

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var (
	redisCmdDuration *prometheus.HistogramVec
	redisCmdCount    *prometheus.CounterVec
	redisOnce        sync.Once
)

func registerRedisMetrics() {
	// 注册指标
	redisOnce.Do(func() {
		redisCmdDuration = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: GetPrometheusConfig().Namespace,
				Subsystem: GetPrometheusConfig().Subsystem,
				Name:      "http_server_duration_seconds",
				Help:      "Duration of HTTP server requests",
				Buckets:   prometheus.DefBuckets,
			},
			[]string{"path", "method"},
		)

		redisCmdCount = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: GetPrometheusConfig().Namespace,
				Subsystem: GetPrometheusConfig().Subsystem,
				Name:      "http_server_status_total",
				Help:      "HTTP server response status codes",
			},
			[]string{"path", "method", "status", "biz_code"},
		)
		prometheus.MustRegister(redisCmdDuration, redisCmdCount)
	})
}

// RedisHook implements go-redis Hook interface
type RedisHook struct{}

func NewRedisHook() *RedisHook {
	registerRedisMetrics()
	return &RedisHook{}
}

func (h *RedisHook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	ctx = context.WithValue(ctx, "redisStart", time.Now())
	return ctx, nil
}

func (h *RedisHook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	start, ok := ctx.Value("redisStart").(time.Time)
	if !ok {
		return nil
	}
	duration := time.Since(start).Seconds()
	cmdName := cmd.Name()
	status := "ok"
	if errors.Is(cmd.Err(), redis.Nil) {
		status = "miss"
	} else if cmd.Err() != nil {
		status = "error"
	}

	redisCmdDuration.WithLabelValues(cmdName, status).Observe(duration)
	redisCmdCount.WithLabelValues(cmdName, status).Inc()
	return nil
}

// Optional: implement pipeline hook if you use pipelining
func (h *RedisHook) BeforeProcessPipeline(ctx context.Context, cmds []redis.Cmder) (context.Context, error) {
	ctx = context.WithValue(ctx, "redisStart", time.Now())
	return ctx, nil
}

func (h *RedisHook) AfterProcessPipeline(ctx context.Context, cmds []redis.Cmder) error {
	start, ok := ctx.Value("redisStart").(time.Time)
	if !ok {
		return nil
	}
	duration := time.Since(start).Seconds()
	status := "ok"

	for _, cmd := range cmds {
		if cmd.Err() != nil && !errors.Is(cmd.Err(), redis.Nil) {
			status = "error"
			break
		}
	}
	redisCmdDuration.WithLabelValues("pipeline", status).Observe(duration)
	redisCmdCount.WithLabelValues("pipeline", status).Inc()
	return nil
}
