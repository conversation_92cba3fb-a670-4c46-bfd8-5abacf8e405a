/**
 * @note
 * mysql
 *
 * <AUTHOR>
 * @date 	2025-06-10
 */
package prometheus

import (
	"fmt"
	"gitlab.docsl.com/security/common"
	"gorm.io/gorm"
	gormprom "gorm.io/plugin/prometheus"
	"sync"
)

var (
	gormMetricPluginPool = new(sync.Map)
)

func GormMetrics(dbName string) gorm.Plugin {
	if GetPrometheusConfig().Addr == common.StringEmpty {
		return nil
	}
	// 已有plugin，复用
	if metricPlugin, ok := gormMetricPluginPool.Load(dbName); ok {
		return metricPlugin.(gorm.Plugin)
	}
	// 没有，new一个
	metricPlugin := gormprom.New(gormprom.Config{
		DBName: dbName,
		MetricsCollector: []gormprom.MetricsCollector{
			&gormprom.MySQL{
				Prefix: fmt.Sprintf("%s_%s_gorm_status_",
					GetPrometheusConfig().Namespace, GetPrometheusConfig().Subsystem),
			},
		},
		StartServer: false,
	})
	gormMetricPluginPool.Store(dbName, metricPlugin)
	return metricPlugin
}
