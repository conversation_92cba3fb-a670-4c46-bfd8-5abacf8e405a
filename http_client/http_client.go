/**
 * @note
 * http_client.go
 *
 * <AUTHOR>
 * @date 	2020-09-10
 */
package http_client

import (
	"gitlab.docsl.com/security/common/masker"
	"net/http"

	"github.com/sirupsen/logrus"
	goResty "github.com/tmsong/go-resty"
	"github.com/tmsong/hlog"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/logger"
)

const (
	MaxPrintBodyLen = 1e6
)

func NewHttpClient(logger *hlog.Logger, printLog bool) (c *goResty.Client) {
	c = goResty.New()
	c.PrintLog = printLog
	c.SetLogger(logger)
	c.OnResponseLog(httpLoggerFunc)
	return
}

func httpLoggerFunc(l goResty.Logger, rl *goResty.ResponseLog) error {
	var err error
	if !common.IsNil(rl.Error()) || rl.ResCode() != http.StatusOK {
		if !common.IsNil(rl.Error()) {
			err = common.NewError(common.ErrCodeHttp, rl.Error())
		} else {
			err = common.NewError(common.ErrCodeHttp)
		}
	} else {
		err = common.NewError(common.ErrCodeOK)
	}
	f := logrus.Fields{
		"tag":       common.LogTagRequestOK,
		"api":       rl.UrlPath(),
		"proc_time": float64(rl.ProcTime().Nanoseconds() / 1e6),
		"host":      rl.Host(),
		"url":       rl.UrlString(),
		"out":       masker.MaskJsonString(l, rl.ResBody(MaxPrintBodyLen, false)),
		"get":       masker.Mask(l, rl.UrlQuery()),
		"post":      masker.MaskJsonString(l, rl.ReqBody(MaxPrintBodyLen, false)),
		"header":    masker.Mask(l, rl.ReqHeader()),
		"method":    rl.ReqMethod(),
		"httpCode":  rl.ResCode(),
	}
	if hl, ok := l.(*hlog.Logger); ok && hl != nil {
		logger.PrintLogWithError(err, hl, f)
	}
	return nil
}

func httpLoggerFuncWithMetric(l goResty.Logger, rl *goResty.ResponseLog) error {
	httpClientPrometheusMetrics()
}
