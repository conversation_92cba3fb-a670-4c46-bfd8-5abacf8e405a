/**
 * @note
 * Http Server相关功能封装
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package http_server

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	"gitlab.docsl.com/security/common/prometheus"
	"log"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/cors"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/proxy"
)

func getAllowOriginFunc() func(iris.Context, string) bool {
	if IsProd() {
		return func(ctx iris.Context, domain string) bool {
			if slices.Contains(GetConfig().AllowOrigins, "*") {
				return true
			}
			return slices.Contains(GetConfig().AllowOrigins, domain)
		}
	} else {
		return func(ctx iris.Context, s string) bool { return true }
	}
}

/* @note
 * 优雅退出
 */
func GracefulExit(servers ...*iris.Application) {
	sig := <-common.SignalChan
	log.Printf("got a signal [%v]\n", sig)
	now := time.Now()
	wg := common.WaitGroupWrapper{}
	ctx, cancel := context.WithTimeout(context.Background(), common.GracefulTimeout)
	defer cancel()
	for idx := range servers {
		i := idx
		wg.Wrap(func() {
			err := servers[i].Shutdown(ctx)
			if err != nil {
				log.Printf("shutdown error [%v]", err)
			}
		})
	}
	wg.Wait()
	log.Println("all http server exited, use time", time.Since(now))
	close(common.QuitChan)
}

func CustomServer(l *hlog.Logger, needCloneLogger bool) *iris.Application {
	app := iris.New()
	app.UseRouter(
		cors.New().AllowOriginFunc(getAllowOriginFunc()).
			AllowHeaders(common.AllowHttpHeaders...).
			ExposeHeaders(common.ExposeHttpHeaders...).
			Handler())
	app.UseRouter(SetLoggerToCtx(l, needCloneLogger))          // 将logger塞入ctx中使用
	app.UseRouter(prometheus.HttpServerPrometheusMiddleware()) // 监控插件
	app.UseRouter(RecordAccessAndRender())                     // 记录日志并渲染
	app.UseRouter(Recovery())                                  // recover
	return app
}

/* @note
 * 将logger塞入gctx，可以选择是否需要clone，=true一个请求一个logger，=false全局共用一个logger
 */
func SetLoggerToCtx(l *hlog.Logger, needClone bool) iris.Handler {
	if l == nil {
		panic("nil logger")
	}
	return func(ctx iris.Context) {
		if needClone {
			logID := common.GenerateId()
			l = l.Clone(logID)
			// 将请求中的requestID和logger中的traceID同步
			if requestID, ok := ctx.GetID().(string); ok && requestID != "" {
				l.SetTraceId(requestID)
			} else if requestID = ctx.GetHeader(common.XRequestIDHeaderKey); requestID != "" {
				l.SetTraceId(requestID)
			} else {
				ctx.Values().Set(common.XRequestIDHeaderKey, l.GetTraceId())
				ctx.ResponseWriter().Header().Set(common.XRequestIDHeaderKey, l.GetTraceId())
				ctx.SetID(l.GetTraceId())
			}
		}
		ctx.Values().Set(common.KeyLogger, l)
		ctx.Next()
	}
}

/* @note
 * 用于记录入参和出参
 */
func RecordAccessAndRender() iris.Handler {
	return func(ctx iris.Context) {
		field := hlog.GetLogField(common.LogTagAccessOut)
		l := common.GetLogger(ctx)
		getInput := ctx.Request().URL.Query()
		postInput := ctx.FormValues()
		addr := ctx.Request().Header.Get("X-Real-IP")
		if addr == "" {
			addr = ctx.Request().Header.Get("X-Forwarded-For")
			if addr == "" {
				addr = ctx.Request().RemoteAddr
			}
		}
		ctx.RecordRequestBody(true)
		body, _ := ctx.GetBody()
		f := logrus.Fields{
			"tag":    common.LogTagAccessIn,
			"url":    ctx.Request().URL.Path,
			"get":    masker.Mask(l, getInput),
			"post":   masker.Mask(l, postInput),
			"body":   strings.Replace(string(masker.MaskJsonBytes(l, body)), "\n", "", -1),
			"method": ctx.Request().Method,
			"host":   addr,
			"header": masker.Mask(l, ctx.Request().Header),
			"domain": ctx.Request().Host,
		}
		logger.PrintLogWithError(nil, l, f)
		////////////////////////////////////////////////////////
		ctx.Next()
		////////////////////////////////////////////////////////
		ret := common.GetRet(ctx)
		api := ctx.Request().Method + ctx.Request().URL.Path
		var eSys *common.Error
		var outputWithErrDesc []byte
		var renderErr error
		switch ret := ret.(type) {
		case *common.Error:
			outputWithErrDesc = masker.Mask(l, ret).(*common.Error).Json()
			eSys = ret
			renderErr = RenderError(ctx, ret)
		case common.Error:
			outputWithErrDesc = masker.Mask(l, ret).(common.Error).Json()
			eSys = &ret
			renderErr = RenderError(ctx, &ret)
		case []byte:
			outputWithErrDesc = masker.MaskJsonBytes(l, ret)
			renderErr = RenderBytes(ctx, ret)
		case string:
			outputWithErrDesc = masker.MaskJsonBytes(l, []byte(ret))
			renderErr = RenderBytes(ctx, []byte(ret))
		case proxy.ProxyResult:
			outputWithErrDesc = masker.MaskJsonBytes(l, ret)
			renderErr = RenderProxyResult(ctx, ret)
		default:
			if statusCode := ctx.GetStatusCode(); statusCode != http.StatusOK {
				eSys = common.NewErrorWithMessage(statusCode, http.StatusText(statusCode))
			} else {
				eSys = common.NewError(common.ErrCodeSys, fmt.Sprintf("unknown error"))
			}
			if ctx.ResponseWriter().Written() == 0 {
				outputWithErrDesc = eSys.Json()
				renderErr = RenderError(ctx, eSys)
			} else {
				outputWithErrDesc = []byte("The response has already been written.")
			}
		}
		//print log
		f = logrus.Fields{
			"status": ctx.GetStatusCode(),
			"url":    ctx.Request().URL.Path,
			"get":    masker.Mask(l, getInput),
			"post":   masker.Mask(l, postInput),
			"body":   string(masker.MaskJsonBytes(l, body)),
			"method": ctx.Request().Method,
			"out":    string(outputWithErrDesc),
			"host":   addr,
			"uri":    api,
			"header": masker.Mask(l, ctx.ResponseWriter().Header()),
			"domain": ctx.Request().Host,
		}
		if renderErr != nil {
			f["render_error"] = renderErr
		}
		logger.PrintLogWithError(eSys, l, field, f)
	}
}

func RenderJSON(ctx iris.Context, output, outputWithErrDesc interface{}) error {
	if IsProd() {
		return ctx.JSON(output)
	} else {
		return ctx.JSON(outputWithErrDesc)
	}
}

// 渲染公共定义的Error结构体
func RenderError(ctx iris.Context, output *common.Error) error {
	if IsProd() {
		return ctx.JSON(output.ExcludeStack())
	} else {
		return ctx.JSON(output)
	}
}

func RenderBytes(ctx iris.Context, output []byte) error {
	if json.Valid(output) {
		ctx.ContentType("application/json; charset=utf-8")
	} else {
		ctx.ContentType("text/plain; charset=utf-8")
	}
	_, err := ctx.Write(output)
	return err
}

func RenderProxyResult(ctx iris.Context, output proxy.ProxyResult) error {
	_, err := ctx.Write(output)
	return err
}

/* @note
 * 从panic中恢复的中间件
 */
func Recovery(alarmFunc ...func(ctx context.Context, err any, stack string)) iris.Handler {
	return func(ctx iris.Context) {
		defer func() {
			if err := recover(); err != nil {
				stack := common.IdentifyPanic()
				var output *common.Error
				switch e := err.(type) {
				case *common.Error:
					output = e.AppendStack(stack)
				default:
					output = common.NewError(common.ErrCodeUnknown).AppendStack(err).AppendStack(stack)
				}
				common.SetRet(ctx, output)
				for _, fn := range alarmFunc {
					fn(ctx, err, stack)
				}
				return
			}
		}()
		ctx.Next()
	}
}
